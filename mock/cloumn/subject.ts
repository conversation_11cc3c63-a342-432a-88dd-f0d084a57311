import { MockMethod } from 'vite-plugin-mock';
import { resultPageSuccess, baseUrl } from '../_util';

const gradeSubject = [
  {
    subject: '语文',
    sortNo: '1',
    remark: '备注',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
  },
  {
    subject: '数学',
    sortNo: '2',
    remark: '备注',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
  },
  {
    subject: '英语',
    sortNo: '3',
    remark: '备注',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
  },
  {
    subject: '物理',
    sortNo: '1',
    remark: '备注',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
  },
  {
    subject: '化学',
    sortNo: '1',
    remark: '备注',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
  },
  {
    subject: '生物',
    sortNo: '1',
    remark: '备注',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
  },
];

export default [
  {
    url: `${baseUrl}/cloumn/subject/list`, // 请求地址
    method: 'get',
    timeout: 1000,
    response: ({ query }) => {
      const { page = 1, pageSize = 20 } = query;
      return resultPageSuccess(page, pageSize, gradeSubject);
    },
  },
] as MockMethod[];
