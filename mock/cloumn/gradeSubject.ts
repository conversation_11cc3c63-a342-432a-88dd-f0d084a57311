import { MockMethod } from 'vite-plugin-mock';
import { resultPageSuccess, baseUrl } from '../_util';
// import { ResultEnum } from '../../src/enums/httpEnum';

const gradeSubject = [
  {
    gradeSubject: '1年级',
    sortNo: '1',
    textBook: '-',
    umber: '-',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
    level: 1,
    children: [
      {
        gradeSubject: '语文',
        sortNo: '1',
        textBook: '统编版、人教版、苏教版',
        umber: '上册、下册',
        lastOperatingTime: '2021-08-08 10:10:10',
        lastOperatingResort: '寒玉',
        level: 2,
      },
      {
        gradeSubject: '数学',
        sortNo: '2',
        textBook: '统编版、人教版、苏教版',
        umber: '上册、下册',
        lastOperatingTime: '2021-08-08 10:10:10',
        lastOperatingResort: '寒玉',
        level: 2,
      },
      {
        gradeSubject: '英语',
        sortNo: '3',
        textBook: '统编版',
        umber: '全一册',
        lastOperatingTime: '2021-08-08 10:10:10',
        lastOperatingResort: '寒玉',
        level: 2,
      },
      {
        gradeSubject: '生物',
        sortNo: '4',
        textBook: '统编版、人教版、苏教版',
        umber: '上册、下册',
        lastOperatingTime: '2021-08-08 10:10:10',
        lastOperatingResort: '寒玉',
        level: 2,
      },
    ],
  },
  {
    gradeSubject: '2年级',
    sortNo: '2',
    textBook: '-',
    umber: '-',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
    level: 1,
  },
  {
    gradeSubject: '3年级',
    sortNo: '3',
    textBook: '-',
    umber: '-',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
    level: 1,
  },
  {
    gradeSubject: '4年级',
    sortNo: '4',
    textBook: '-',
    umber: '-',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
    level: 1,
  },
  {
    gradeSubject: '5年级',
    sortNo: '5',
    textBook: '-',
    umber: '-',
    lastOperatingTime: '2021-08-08 10:10:10',
    lastOperatingResort: '寒玉',
    level: 1,
  },
];

export default [
  {
    url: `${baseUrl}/cloumn/grandeSubject/list`, // 请求地址
    method: 'get',
    timeout: 1000,
    response: ({ query }) => {
      const { page = 1, pageSize = 20 } = query;
      return resultPageSuccess(page, pageSize, gradeSubject);
    },
  },
] as MockMethod[];
