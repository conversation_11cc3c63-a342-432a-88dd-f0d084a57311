<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit" width="700px">
    <a-form
      class="form"
      ref="formRef"
      :model="formState"
      name="validate_other"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :style="{ 'max-height': formMaxHeight + 'px' }"
    >
      <a-row :gutter="8">
        <a-col :span="12">
          <a-form-item
            name="series"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="系列"
            :rules="[{ required: true, message: '请选择系列!' }]"
          >
            <a-select
              v-model:value="formState.series"
              show-search
              allow-clear
              optionFilterProp="label"
              placeholder="请选择系列"
              :options="seriesOptions"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            name="year"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="年份"
            :rules="[{ required: true, message: '请选择年份!' }]"
          >
            <a-select
              v-model:value="formState.year"
              show-search
              allow-clear
              placeholder="请选择年份"
              :options="yearOptions"
              optionFilterProp="label"
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item name="appModId" label="应用模块" style="margin-bottom: 16px" :rules="[{ required: true, message: '请选择应用模块' }]">
        <a-cascader
          v-model:value="formState.appModId"
          show-search
          placeholder="请选择应用模块"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          :options="appModuleOptions"
        />
      </a-form-item>
      <a-row :gutter="8">
        <a-col :span="12">
          <a-form-item
            name="period"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="学段"
            :rules="[{ required: true, message: '请选择学段!' }]"
          >
            <a-select
              v-model:value="formState.period"
              show-search
              allow-clear
              placeholder="请选择学段"
              :options="periodOptions"
              :fieldNames="{
                label: 'name',
                value: 'code',
              }"
              @change="handlePeriodChange"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            name="subject"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="学科"
            :rules="[{ required: true, message: '请选择学科!' }]"
          >
            <a-select
              v-model:value="formState.subject"
              show-search
              allow-clear
              placeholder="请选择学科"
              :options="subjectOptions"
              :fieldNames="{
                label: 'name',
                value: 'code',
              }"
              @change="handleSubjectChange"
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="8">
        <a-col :span="12">
          <a-form-item
            name="press"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="版本"
            :rules="[{ required: true, message: '请选择版本!' }]"
          >
            <a-select
              v-model:value="formState.press"
              show-search
              allow-clear
              placeholder="请选择版本"
              :options="pressOptions"
              :fieldNames="{
                label: 'name',
                value: 'code',
              }"
              @change="handlePressChange"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            name="grade"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="年级"
            :rules="[{ required: true, message: '请选择年级!' }]"
          >
            <a-select
              v-model:value="formState.grade"
              show-search
              allow-clear
              placeholder="请选择年级"
              :options="gradeOptions"
              :fieldNames="{
                label: 'name',
                value: 'code',
              }"
              @change="handleGradeChange"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            name="volume"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="册次"
            :rules="[{ required: true, message: '请选择册次!' }]"
          >
            <a-select
              v-model:value="formState.volume"
              show-search
              allow-clear
              placeholder="请选择册次"
              :options="volumeOptions"
              :fieldNames="{
                label: 'name',
                value: 'code',
              }"
              @change="handleVolumeChange"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            name="columns"
            :label-col="{
              span: 8,
            }"
            :wrapper-col="{
              span: 14,
            }"
            label="列数"
            :rules="[{ required: true, message: '请选择列数!' }]"
          >
            <a-select
              v-model:value="formState.columns"
              show-search
              allow-clear
              placeholder="请选择列数"
              :options="[
                {
                  label: '1列',
                  value: 1,
                },
                {
                  label: '2列',
                  value: 2,
                },
                {
                  label: '3列',
                  value: 3,
                },
                {
                  label: '4列',
                  value: 4,
                },
                {
                  label: '5列',
                  value: 5,
                },
                {
                  label: '6列',
                  value: 6,
                },
                {
                  label: '7列',
                  value: 7,
                },
                {
                  label: '8列',
                  value: 8,
                },
              ]"
            ></a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item name="bookNumber" label="书号" :rules="[{ required: true, message: '请输入书号!' }]">
        <a-input v-model:value="formState.bookNumber" placeholder="请输入书号" />
      </a-form-item>
      <a-form-item name="resCover" label="资源封面">
        <a-upload
          accept=".jpeg,.jpg,.png,.pdf"
          :maxCount="1"
          @preview="() => false"
          v-model:file-list="formState.resCover"
          list-type="picture"
          :before-upload="beforeResCoverUpload"
          :custom-request="customRequestResCover"
        >
          <a-button>
            <upload-outlined></upload-outlined>
            {{ formState.resCover.length > 0 ? '替换封面' : '上传' }}
          </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item name="outType" label="输出方向" :rules="[{ required: true, message: '请选择输出方向!', type: 'array' }]">
        <a-checkbox-group v-model:value="formState.outType">
          <a-checkbox value="02" name="type">对外输出</a-checkbox>
          <a-checkbox value="01" name="type">对内输出</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item name="types" label="资源类型" :rules="[{ required: true, message: '请选择资源类型!', type: 'array' }]">
        <a-checkbox-group v-model:value="formState.types" @change="handleTypesChange">
          <a-checkbox value="101" name="type">正文</a-checkbox>
          <a-checkbox value="102" name="type">答案</a-checkbox>
          <a-checkbox value="103" name="type">试卷</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      <div class="config-box" v-if="formState.types.includes('101')">
        <a-form-item name="nameOne" label="正文标题" :rules="getValidationRules('101', 'name')">
          <a-input v-model:value="formState.nameOne" placeholder="请输入正文标题" />
        </a-form-item>
        <a-form-item name="filesOne" label="资源文件" :rules="getValidationRules('101', 'file')">
          <a-upload-dragger
            v-model:fileList="formState.filesOne"
            name="resFiles"
            accept=".doc,.docx,.pdf"
            :max-count="1"
            :before-upload="beforeUpload"
            :custom-request="customRequestFilesOne"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">单击或拖动文件到此区域进行上传</p>
            <p class="ant-upload-hint">支持doc,docx,pdf格式文件单个上传，单文件限制1GB</p>
            <template #itemRender="{ file }">
              <a-space class="bc-upload-tips">
                <div class="upload-tips-left">
                  <span :title="file.name" class="filename">{{ file.name }}</span>
                  <span class="filesize">{{
                    file.size < 1048576
                      ? (file.size / 1024).toFixed(2) + ' KB'
                      : file.size >= 1048576
                      ? (file.size / 1024 / 1024).toFixed(2) + ' MB'
                      : ''
                  }}</span>
                  <span
                    class="filestatus"
                    :style="file.status === 'error' || file.status === 'error-toolarge' || file.status === 'error-code' ? 'color: red' : ''"
                    >{{
                      file.status === 'done'
                        ? '上传完成'
                        : file.status === 'uploading'
                        ? '上传中...'
                        : file.status === 'error'
                        ? '上传失败（未知原因）'
                        : file.status === 'error-toolarge'
                        ? '上传失败（文件过大）'
                        : ''
                    }}</span
                  >
                </div>
                <a-button class="delete-btn" type="text" danger @click="removeRes('101')">
                  <DeleteOutlined class="delete-icon" />
                  删除
                </a-button>
              </a-space>
            </template>
          </a-upload-dragger>
        </a-form-item>
        <a-form-item name="textOne" label="拆分规则" :rules="getValidationRules('101', 'text')" style="margin-bottom: 0">
          <a-textarea
            v-model:value="formState.textOne"
            :rows="13"
            :placeholder="'##代表章。每行以回车符结尾，每行#符号后是章节标题，@@符号后为当前章节的起始页码，例：\n##第一单元\n###1春@@7\n###2济南的春天@@13\n###3雨的四季@@19\n###4吉代诗歌四首@@25\n##第一单元\n###5秋天的怀念@@26\n###6散先@@31\n###7散文诗二首@38\n###8《世说新语》二则@42'"
            :maxlength="4000"
          />
        </a-form-item>
      </div>

      <div class="config-box" v-if="formState.types.includes('102')">
        <a-form-item name="nameTwo" label="答案标题" :rules="getValidationRules('102', 'name')">
          <a-input v-model:value="formState.nameTwo" placeholder="请输入答案标题" />
        </a-form-item>
        <a-form-item name="filesTwo" label="资源文件" :rules="getValidationRules('102', 'file')">
          <a-upload-dragger
            v-model:fileList="formState.filesTwo"
            name="resFiles"
            accept=".doc,.docx,.pdf"
            :max-count="1"
            :before-upload="beforeUpload"
            :custom-request="customRequestFilesTwo"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">单击或拖动文件到此区域进行上传</p>
            <p class="ant-upload-hint">支持doc,docx,pdf格式文件单个上传，单文件限制1GB</p>
            <template #itemRender="{ file }">
              <a-space class="bc-upload-tips">
                <div class="upload-tips-left">
                  <span :title="file.name" class="filename">{{ file.name }}</span>
                  <span class="filesize">{{
                    file.size < 1048576
                      ? (file.size / 1024).toFixed(2) + ' KB'
                      : file.size >= 1048576
                      ? (file.size / 1024 / 1024).toFixed(2) + ' MB'
                      : ''
                  }}</span>
                  <span
                    class="filestatus"
                    :style="file.status === 'error' || file.status === 'error-toolarge' || file.status === 'error-code' ? 'color: red' : ''"
                    >{{
                      file.status === 'done'
                        ? '上传完成'
                        : file.status === 'uploading'
                        ? '上传中...'
                        : file.status === 'error'
                        ? '上传失败（未知原因）'
                        : file.status === 'error-toolarge'
                        ? '上传失败（文件过大）'
                        : ''
                    }}</span
                  >
                </div>
                <a-button class="delete-btn" type="text" danger @click="removeRes('102')">
                  <DeleteOutlined class="delete-icon" />
                  删除
                </a-button>
              </a-space>
            </template>
          </a-upload-dragger>
        </a-form-item>
        <a-form-item name="textTwo" label="拆分规则" :rules="getValidationRules('102', 'text')" style="margin-bottom: 0">
          <a-textarea
            v-model:value="formState.textTwo"
            :rows="13"
            :placeholder="'##代表章。每行以回车符结尾，每行#符号后是章节标题，@@符号后为当前章节的起始页码，例：\n##第一单元\n###1春@@7\n###2济南的春天@@13\n###3雨的四季@@19\n###4吉代诗歌四首@@25\n##第一单元\n###5秋天的怀念@@26\n###6散先@@31\n###7散文诗二首@38\n###8《世说新语》二则@42'"
            :maxlength="4000"
          />
        </a-form-item>
      </div>

      <div class="config-box" v-if="formState.types.includes('103')">
        <a-form-item name="nameThree" label="试卷标题" :rules="getValidationRules('103', 'name')">
          <a-input v-model:value="formState.nameThree" placeholder="请输入试卷标题" />
        </a-form-item>
        <a-form-item name="filesThree" label="资源文件" :rules="getValidationRules('103', 'file')">
          <a-upload-dragger
            v-model:fileList="formState.filesThree"
            name="resFiles"
            accept=".doc,.docx,.pdf"
            :max-count="1"
            :before-upload="beforeUpload"
            :custom-request="customRequestFilesThree"
          >
            <p class="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p class="ant-upload-text">单击或拖动文件到此区域进行上传</p>
            <p class="ant-upload-hint">支持doc,docx,pdf格式文件单个上传，单文件限制1GB</p>
            <template #itemRender="{ file }">
              <a-space class="bc-upload-tips">
                <div class="upload-tips-left">
                  <span :title="file.name" class="filename">{{ file.name }}</span>
                  <span class="filesize">{{
                    file.size < 1048576
                      ? (file.size / 1024).toFixed(2) + ' KB'
                      : file.size >= 1048576
                      ? (file.size / 1024 / 1024).toFixed(2) + ' MB'
                      : ''
                  }}</span>
                  <span
                    class="filestatus"
                    :style="file.status === 'error' || file.status === 'error-toolarge' || file.status === 'error-code' ? 'color: red' : ''"
                    >{{
                      file.status === 'done'
                        ? '上传完成'
                        : file.status === 'uploading'
                        ? '上传中...'
                        : file.status === 'error'
                        ? '上传失败（未知原因）'
                        : file.status === 'error-toolarge'
                        ? '上传失败（文件过大）'
                        : ''
                    }}</span
                  >
                </div>
                <a-button class="delete-btn" type="text" danger @click="removeRes('103')">
                  <DeleteOutlined class="delete-icon" />
                  删除
                </a-button>
              </a-space>
            </template>
          </a-upload-dragger>
        </a-form-item>
        <a-form-item name="textThree" label="拆分规则" :rules="getValidationRules('103', 'text')" style="margin-bottom: 0">
          <a-textarea
            v-model:value="formState.textThree"
            :rows="13"
            :placeholder="'##代表章。每行以回车符结尾，每行#符号后是章节标题，@@符号后为当前章节的起始页码，例：\n##第一单元\n###1春@@7\n###2济南的春天@@13\n###3雨的四季@@19\n###4吉代诗歌四首@@25\n##第一单元\n###5秋天的怀念@@26\n###6散先@@31\n###7散文诗二首@38\n###8《世说新语》二则@42'"
            :maxlength="4000"
          />
        </a-form-item>
      </div>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, unref, reactive, ref, nextTick } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { InboxOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { addOrUpdate, ebookSeries } from './../configuration.api';
import {
  appModuleList,
  selectYear,
  selectPeriod,
  selectSubject,
  selectPress,
  selectGrade,
  selectVolume,
} from '/@/api/common/common.dictionary';
import uploadFile from '/@/utils/uploadImg/baidubceUpload';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

// 声明Emits
const emit = defineEmits(['success', 'register']);
const isUpdate = ref(true);
const formMaxHeight = ref(0);

const labelCol = reactive({
  span: 4,
});
const wrapperCol = reactive({
  span: 19,
});
const formRef = ref<FormInstance>();
const formState = reactive<any>({
  id: undefined,
  appModId: undefined,
  series: undefined,
  year: undefined,
  period: undefined,
  subject: undefined,
  grade: undefined,
  press: undefined,
  volume: undefined,
  columns: '1',
  bookNumber: undefined,
  outType: ['02'],
  types: ['101'],
  nameOne: undefined,
  filesOne: [],
  fileIdOne: undefined,
  fileNameOne: undefined,
  fileSizeOne: undefined,
  fileUrlOne: undefined,
  resCover: [],
  resCoverId: undefined,
  resCoverName: undefined,
  resCoverSize: undefined,
  resCoverUrl: undefined,

  filesTwo: [],
  nameTwo: undefined,
  fileIdTwo: undefined,
  fileNameTwo: undefined,
  fileSizeTwo: undefined,
  fileUrlTwo: undefined,
  textTwo: undefined,

  filesThree: [],
  nameThree: undefined,
  fileIdThree: undefined,
  fileNameThree: undefined,
  fileSizeThree: undefined,
  fileUrlThree: undefined,
  textThree: undefined,
});
const appModuleOptions = ref<any>([]);
const seriesOptions = ref<any>([]);
const yearOptions = ref<any>([]);
const periodOptions = ref<any>([]);
const gradeOptions = ref<any>([]);
const subjectOptions = ref<any>([]);
const pressOptions = ref<any>([]);
const volumeOptions = ref<any>([]);

const getSeriesData = async () => {
  seriesOptions.value = await ebookSeries();
};
getSeriesData();

const getYearData = async () => {
  yearOptions.value = await selectYear();
};
getYearData();

const getPeriodData = async () => {
  periodOptions.value = await selectPeriod();
};
getPeriodData();
//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: false, minHeight: 80 });
  formMaxHeight.value = document.body.clientHeight - 350;
  isUpdate.value = !!data.isUpdate;

  formRef.value?.resetFields();
  // 初始化数据
  formState.id = undefined;
  formState.year = undefined;
  formState.period = undefined;
  formState.subject = undefined;
  formState.grade = undefined;
  formState.press = undefined;
  formState.volume = undefined;
  formState.columns = undefined;
  formState.bookNumber = undefined;
  formState.outType = ['02'];
  formState.types = ['101'];

  formState.resCover = [];
  formState.resCoverId = undefined;
  formState.resCoverName = undefined;
  formState.resCoverSize = undefined;
  formState.resCoverUrl = undefined;

  formState.nameOne = undefined;
  formState.filesOne = [];
  formState.fileIdOne = undefined;
  formState.fileNameOne = undefined;
  formState.fileSizeOne = undefined;
  formState.fileUrlOne = undefined;
  formState.textOne = undefined;

  formState.nameTwo = undefined;
  formState.filesTwo = [];
  formState.fileIdTwo = undefined;
  formState.fileNameTwo = undefined;
  formState.fileSizeTwo = undefined;
  formState.fileUrlTwo = undefined;
  formState.textTwo = undefined;

  formState.nameThree = undefined;
  formState.filesThree = [];
  formState.fileIdThree = undefined;
  formState.fileNameThree = undefined;
  formState.fileSizeThree = undefined;
  formState.fileUrlThree = undefined;
  formState.textThree = undefined;

  // 编辑，设置表单值
  if (isUpdate.value) {
    const record = data.record;
    console.log(record);
    formState.id = record.id;
    formState.series = record.series;
    formState.appModId = record.appModId?.split(',');
    formState.year = record.year;
    formState.period = record.period;
    formState.subject = record.subject;
    formState.grade = record.grade;
    formState.press = record.press;
    formState.volume = record.volume;
    formState.columns = record.columns;
    formState.bookNumber = record.bookNumber;
    formState.outType = record.outType ?? [];

    // 根据 configResList 设置 types
    const configTypes: string[] = [];
    if (record.configResList && record.configResList.length > 0) {
      record.configResList.forEach((config: any) => {
        if (config.type && !configTypes.includes(config.type)) {
          configTypes.push(config.type);
        }
      });
    }
    formState.types = configTypes.length > 0 ? configTypes : ['101'];

    formState.resCoverId = record.resCoverId;
    formState.resCoverName = record.resCoverName;
    formState.resCoverSize = record.resCoverSize;
    formState.resCoverUrl = record.resCoverUrl;
    if (formState.resCoverId == undefined) {
      formState.resCover = [];
    } else {
      formState.resCover = [
        {
          uid: record.resCoverId,
          size: +record.resCoverSize,
          name: record.resCoverName,
          status: 'done',
          url: record.resCoverUrl,
        },
      ];
    }
    record.configResList = record.configResList ?? [];
    console.log('=== 编辑时的 configResList 数据 ===', record.configResList);
    record.configResList.forEach((element) => {
      console.log('=== 处理配置项 ===', element);
      console.log('=== 配置项的所有字段 ===', Object.keys(element));
      console.log('=== 查找URL相关字段 ===', {
        fileUrl: element.fileUrl,
        filePath: element.filePath,
        url: element.url,
        downloadUrl: element.downloadUrl,
        fileAddress: element.fileAddress,
        path: element.path,
        link: element.link
      });
      if (element.type == '101') {
        formState.nameOne = element.name;
        formState.filesOne = [];
        // 使用正确的字段名
        formState.fileIdOne = element.fileId;
        formState.fileNameOne = element.fileName;
        formState.fileSizeOne = element.fileSize;
        // 使用系统的文件访问URL工具函数
        formState.fileUrlOne = getFileAccessHttpUrl(element.filePath || element.fileUrl || element.fileId);
        formState.textOne = element.text;

        console.log('=== 正文文件信息 ===', {
          fileIdOne: formState.fileIdOne,
          fileNameOne: formState.fileNameOne,
          fileSizeOne: formState.fileSizeOne,
          fileUrlOne: formState.fileUrlOne,
          originalFilePath: element.filePath,
          originalFileUrl: element.fileUrl
        });

        if (formState.fileIdOne == undefined) {
          formState.filesOne = [];
        } else {
          formState.filesOne = [
            {
              uid: formState.fileIdOne,
              name: formState.fileNameOne,
              size: +formState.fileSizeOne || 0,
              status: 'done',
              url: formState.fileUrlOne,
            },
          ];
        }
      }
      if (element.type == '102') {
        formState.nameTwo = element.name;
        formState.filesTwo = [];
        // 使用正确的字段名
        formState.fileIdTwo = element.fileId;
        formState.fileNameTwo = element.fileName;
        formState.fileSizeTwo = element.fileSize;
        // 使用系统的文件访问URL工具函数
        formState.fileUrlTwo = getFileAccessHttpUrl(element.filePath || element.fileUrl || element.fileId);
        formState.textTwo = element.text;

        console.log('=== 答案文件信息 ===', {
          fileIdTwo: formState.fileIdTwo,
          fileNameTwo: formState.fileNameTwo,
          fileSizeTwo: formState.fileSizeTwo,
          fileUrlTwo: formState.fileUrlTwo
        });

        if (formState.fileIdTwo == undefined) {
          formState.filesTwo = [];
        } else {
          formState.filesTwo = [
            {
              uid: formState.fileIdTwo,
              name: formState.fileNameTwo,
              size: +formState.fileSizeTwo || 0,
              status: 'done',
              url: formState.fileUrlTwo,
            },
          ];
        }
      }
      if (element.type == '103') {
        formState.nameThree = element.name;
        formState.filesThree = [];
        // 尝试不同的字段名
        formState.fileIdThree = element.fileId || element.id;
        formState.fileNameThree = element.fileName || element.name;
        formState.fileSizeThree = element.fileSize || element.size;
        formState.fileUrlThree = element.fileUrl || element.url || element.filePath;
        formState.textThree = element.text;

        console.log('=== 试卷文件信息 ===', {
          fileIdThree: formState.fileIdThree,
          fileNameThree: formState.fileNameThree,
          fileSizeThree: formState.fileSizeThree,
          fileUrlThree: formState.fileUrlThree
        });

        if (formState.fileIdThree == undefined) {
          formState.filesThree = [];
        } else {
          formState.filesThree = [
            {
              uid: formState.fileIdThree,
              name: formState.fileNameThree,
              size: +formState.fileSizeThree || 0,
              status: 'done',
              url: formState.fileUrlThree,
            },
          ];
        }
      }
    });
    subjectOptions.value = await selectSubject({ period: formState.period });
    pressOptions.value = await selectPress({ period: formState.period, subject: formState.subject });
    console.log(formState.press);
    if (formState.press) {
      console.log(formState.press);
      gradeOptions.value = await selectGrade({ period: formState.period, subject: formState.subject, press: formState.press });
      if (formState.grade) {
        volumeOptions.value = await selectVolume({
          period: formState.period,
          subject: formState.subject,
          press: formState.press,
          grade: formState.grade,
        });
      }
    }
  }
  appModuleOptions.value = await appModuleList({});
});

//获取弹窗标题
const getTitle = computed(() => (!unref(isUpdate) ? '新增资源配置' : '编辑资源配置'));

const handlePeriodChange = async (period) => {
  formState.period = period;
  formState.subject = undefined;
  formState.press = undefined;
  formState.grade = undefined;
  formState.volume = undefined;
  subjectOptions.value = [];
  pressOptions.value = [];
  gradeOptions.value = [];
  volumeOptions.value = [];
  if (period != undefined) {
    subjectOptions.value = await selectSubject({ period });
  }
};
const handleSubjectChange = async (subject) => {
  formState.subject = subject;
  formState.press = undefined;
  formState.grade = undefined;
  formState.volume = undefined;
  pressOptions.value = [];
  gradeOptions.value = [];
  volumeOptions.value = [];
  if (subject != undefined) {
    pressOptions.value = await selectPress({ period: formState.period, subject });
  }
};
const handlePressChange = async (press) => {
  formState.press = press;
  formState.grade = undefined;
  formState.volume = undefined;
  gradeOptions.value = [];
  volumeOptions.value = [];
  if (press != undefined) {
    gradeOptions.value = await selectGrade({ period: formState.period, subject: formState.subject, press });
  }
};
const handleGradeChange = async (grade) => {
  formState.grade = grade;
  formState.volume = undefined;
  volumeOptions.value = [];
  if (formState.press != undefined && grade !== undefined) {
    volumeOptions.value = await selectVolume({ period: formState.period, subject: formState.subject, press: formState.press, grade });
  }
};
const handleVolumeChange = async (volume) => {
  formState.volume = volume;
};

// 处理资源类型变化
const handleTypesChange = (checkedValues) => {
  console.log('资源类型变化:', checkedValues);

  // 如果取消勾选某个类型，清理对应的数据
  if (!checkedValues.includes('101')) {
    clearResourceData('101');
  }
  if (!checkedValues.includes('102')) {
    clearResourceData('102');
  }
  if (!checkedValues.includes('103')) {
    clearResourceData('103');
  }
};

// 清理资源数据
const clearResourceData = (type) => {
  if (type === '101') {
    formState.nameOne = undefined;
    formState.filesOne = [];
    formState.fileIdOne = undefined;
    formState.fileNameOne = undefined;
    formState.fileSizeOne = undefined;
    formState.fileUrlOne = undefined;
    formState.textOne = undefined;
  } else if (type === '102') {
    formState.nameTwo = undefined;
    formState.filesTwo = [];
    formState.fileIdTwo = undefined;
    formState.fileNameTwo = undefined;
    formState.fileSizeTwo = undefined;
    formState.fileUrlTwo = undefined;
    formState.textTwo = undefined;
  } else if (type === '103') {
    formState.nameThree = undefined;
    formState.filesThree = [];
    formState.fileIdThree = undefined;
    formState.fileNameThree = undefined;
    formState.fileSizeThree = undefined;
    formState.fileUrlThree = undefined;
    formState.textThree = undefined;
  }
};

// 动态验证规则
const getValidationRules = (type, field) => {
  const isTypeSelected = formState.types.includes(type);

  if (!isTypeSelected) {
    return [];
  }

  switch (field) {
    case 'name':
      return [{ required: true, message: `请输入${type === '101' ? '正文' : type === '102' ? '答案' : '试卷'}标题!` }];
    case 'file':
      return [{ required: true, message: '请上传资源文件!', type: 'array' }];
    case 'text':
      return [{ required: true, message: '请输入拆分规则!' }];
    default:
      return [];
  }
};

const removeRes = (type) => {
  if (type == '101') {
    formState.nameOne = undefined;
    formState.filesOne = [];
    formState.fileIdOne = undefined;
    formState.fileNameOne = undefined;
    formState.fileSizeOne = undefined;
    formState.fileUrlOne = undefined;
  } else if (type == '102') {
    formState.nameTwo = undefined;
    formState.filesTwo = [];
    formState.fileIdTwo = undefined;
    formState.fileNameTwo = undefined;
    formState.fileSizeTwo = undefined;
    formState.fileUrlTwo = undefined;
  } else if (type == '103') {
    formState.nameThree = undefined;
    formState.filesThree = [];
    formState.fileIdThree = undefined;
    formState.fileNameThree = undefined;
    formState.fileSizeThree = undefined;
    formState.fileUrlThree = undefined;
  }
};
/**
 * 上传之前
 */
// const validatorFiles = () => {
//   if (formState.files.length > 0) {
//     return Promise.resolve();
//   }
//   return Promise.reject(new Error('资源文件不能为空!'));
// }

// const validatorCovers = () => {
//   if (formState.resCover.length > 0) {
//     return Promise.resolve();
//   }
//   return Promise.reject(new Error('请选择资源封面!'));
// }
const beforeUpload = (file) => {
  return new Promise<boolean>((resolve, reject) => {
    const isSize = file.size <= 1024 * 1024 * 1000;
    if (!isSize) {
      file.status = 'error-toolarge';
      reject(false);
    } else {
      resolve(true);
    }
  });
};

const customRequestFilesOne = async (files) => {
  const { file } = files;
  let filelist: any = {};
  const name = file.name.substring(0, file.name.lastIndexOf('.'));
  setTimeout(() => {
    // 如果是编辑模式且已有文件信息，保持原有的文件信息
    if (!formState.fileIdOne) {
      formState.fileIdOne = undefined;
    }
    formState.nameOne = name;
    files.onSuccess(filelist, file);
  }, 0);
};
const customRequestFilesTwo = async (files) => {
  const { file } = files;
  let filelist: any = {};
  const name = file.name.substring(0, file.name.lastIndexOf('.'));
  setTimeout(() => {
    // 如果是编辑模式且已有文件信息，保持原有的文件信息
    if (!formState.fileIdTwo) {
      formState.fileIdTwo = undefined;
    }
    formState.nameTwo = name;
    files.onSuccess(filelist, file);
  }, 0);
};
const customRequestFilesThree = async (files) => {
  const { file } = files;
  let filelist: any = {};
  const name = file.name.substring(0, file.name.lastIndexOf('.'));
  setTimeout(() => {
    // 如果是编辑模式且已有文件信息，保持原有的文件信息
    if (!formState.fileIdThree) {
      formState.fileIdThree = undefined;
    }
    formState.nameThree = name;
    files.onSuccess(filelist, file);
  }, 0);
};
const beforeResCoverUpload = (file) => {
  return new Promise<boolean>((resolve, reject) => {
    // const isImage = file.type === 'image/jpg' || file.type === 'image/jpeg' || file.type === 'image/png';
    const fileSizeInByte = file.size;
    const fileSizeInMB = fileSizeInByte / (1024 * 1024); // 将文件大小转换为 MB
    if (fileSizeInMB > 10) {
      message.warning('文件大小超过限制，最大支持 10M');
      reject(false); // 阻止文件上传
    }
    resolve(true); // 阻止文件上传
  });
};
const customRequestResCover = async (files) => {
  const { file } = files;
  setTimeout(() => {
    formState.resCoverId = undefined;
    formState.resCoverUrl = undefined;
    files.onSuccess({}, file);
  }, 0);
};
//表单提交事件
let submitCounter = 0;
async function handleSubmit() {
  submitCounter++;
  console.log('=== handleSubmit 开始执行 ===', submitCounter, new Date().toISOString());
  console.log('=== formState.types ===', formState.types);
  console.log('=== formState.resCover ===', formState.resCover);
  console.log('=== formState.filesOne ===', formState.filesOne);
  await formRef
    .value!.validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });
        let values: any = {};
        let formatValue: any = {};
        formatValue.id = formState.id;
        formatValue.series = formState.series;
        formatValue.appModId = formState.appModId.join(',');
        formatValue.year = formState.year;
        formatValue.period = formState.period;
        formatValue.subject = formState.subject;
        formatValue.press = formState.press;
        formatValue.grade = formState.grade;
        formatValue.volume = formState.volume;
        formatValue.columns = formState.columns;
        formatValue.bookNumber = formState.bookNumber;
        formatValue.outType = formState.outType;
        formatValue.configResList = [];
        // 上传第资源封面
        console.log('=== 检查资源封面上传条件 ===', {
          resCoverLength: formState.resCover.length,
          resCoverStatus: formState.resCover.length > 0 ? formState.resCover[0].status : 'no file'
        });
        if (formState.resCover.length > 0 && formState.resCover[0].status === 'done') {
          if (formState.resCoverId != undefined) {
            formatValue.resCoverId = formState.resCoverId;
            formatValue.resCoverName = formState.resCoverName;
            formatValue.resCoverSize = formState.resCoverSize;
            formatValue.resCoverUrl = formState.resCoverUrl;
          } else {
            // const expandRes: any = await uploadFile(formState.resCover[0].originFileObj);

            console.log('=== 开始上传资源封面 ===', submitCounter, formState.resCover[0]);
            const addResResult: any = await uploadFile(formState.resCover[0].originFileObj, {
              moduleCode: '140-2',
              grade: formState.grade,
              press: formState.press,
              subject: formState.subject,
              volume: formState.volume,
              seconds: 0,
            });
            console.log('=== 资源封面上传完成 ===', submitCounter, addResResult);
            if (addResResult && addResResult.success) {
              formatValue.resCoverId = addResResult.result.id;
              formatValue.resCoverName = addResResult.result.fileName;
              formatValue.resCoverSize = addResResult.result.fileSize;
              formatValue.resCoverUrl = addResResult.result.filePath;
            }
          }
        } else {
          formatValue.resCoverId = undefined;
          formatValue.resCoverName = undefined;
          formatValue.resCoverSize = undefined;
          formatValue.resCoverUrl = undefined;
        }

        // 处理正文文件（101）
        console.log('=== 检查正文文件 ===', submitCounter, {
          includesType101: formState.types.includes('101'),
          fileIdOne: formState.fileIdOne,
          filesOneLength: formState.filesOne.length,
          filesOneStatus: formState.filesOne.length > 0 ? formState.filesOne[0].status : 'no file'
        });
        if (formState.types.includes('101')) {
          // 如果已有文件ID（编辑模式），直接使用
          if (formState.fileIdOne != undefined) {
            values.fileIdOne = formState.fileIdOne;
            values.fileNameOne = formState.fileNameOne;
            values.fileSizeOne = formState.fileSizeOne;
            values.fileUrlOne = formState.fileUrlOne;
          }
          // 如果有新上传的文件，需要上传
          else if (formState.filesOne.length > 0 && formState.filesOne[0].status === 'done' && formState.filesOne[0].originFileObj) {
            console.log('=== 开始上传正文文件 ===', submitCounter, formState.filesOne[0]);
            const addResResult: any = await uploadFile(formState.filesOne[0].originFileObj, {
              moduleCode: '140-1',
              grade: formState.grade,
              press: formState.press,
              subject: formState.subject,
              volume: formState.volume,
              seconds: 0,
            });
            console.log('=== 正文文件上传完成 ===', submitCounter, addResResult);
            if (addResResult && addResResult.success) {
              values.fileIdOne = addResResult.result.id;
              values.fileNameOne = addResResult.result.fileName;
              values.fileSizeOne = addResResult.result.fileSize;
              values.fileUrlOne = addResResult.result.filePath;
            }
          }
        } else {
          values.fileIdOne = undefined;
          values.fileNameOne = undefined;
          values.fileSizeOne = undefined;
          values.fileUrlOne = undefined;
        }

        // 处理答案文件（102）
        console.log('=== 检查答案文件 ===', submitCounter, {
          includesType102: formState.types.includes('102'),
          fileIdTwo: formState.fileIdTwo,
          filesTwoLength: formState.filesTwo.length,
          filesTwoStatus: formState.filesTwo.length > 0 ? formState.filesTwo[0].status : 'no file'
        });
        if (formState.types.includes('102')) {
          // 如果已有文件ID（编辑模式），直接使用
          if (formState.fileIdTwo != undefined) {
            values.fileIdTwo = formState.fileIdTwo;
            values.fileNameTwo = formState.fileNameTwo;
            values.fileSizeTwo = formState.fileSizeTwo;
            values.fileUrlTwo = formState.fileUrlTwo;
          }
          // 如果有新上传的文件，需要上传
          else if (formState.filesTwo.length > 0 && formState.filesTwo[0].status === 'done' && formState.filesTwo[0].originFileObj) {
            console.log('=== 开始上传答案文件 ===', submitCounter, formState.filesTwo[0]);
            const addResResult: any = await uploadFile(formState.filesTwo[0].originFileObj, {
              moduleCode: '140-1',
              grade: formState.grade,
              press: formState.press,
              subject: formState.subject,
              volume: formState.volume,
              seconds: 0,
            });
            console.log('=== 答案文件上传完成 ===', submitCounter, addResResult);
            if (addResResult && addResResult.success) {
              values.fileIdTwo = addResResult.result.id;
              values.fileNameTwo = addResResult.result.fileName;
              values.fileSizeTwo = addResResult.result.fileSize;
              values.fileUrlTwo = addResResult.result.filePath;
            }
          }
        } else {
          values.fileIdTwo = undefined;
          values.fileNameTwo = undefined;
          values.fileSizeTwo = undefined;
          values.fileUrlTwo = undefined;
        }

        // 处理试卷文件（103）
        console.log('=== 检查试卷文件 ===', submitCounter, {
          includesType103: formState.types.includes('103'),
          fileIdThree: formState.fileIdThree,
          filesThreeLength: formState.filesThree.length,
          filesThreeStatus: formState.filesThree.length > 0 ? formState.filesThree[0].status : 'no file'
        });
        if (formState.types.includes('103')) {
          // 如果已有文件ID（编辑模式），直接使用
          if (formState.fileIdThree !== undefined) {
            values.fileIdThree = formState.fileIdThree;
            values.fileNameThree = formState.fileNameThree;
            values.fileSizeThree = formState.fileSizeThree;
            values.fileUrlThree = formState.fileUrlThree;
          }
          // 如果有新上传的文件，需要上传
          else if (formState.filesThree.length > 0 && formState.filesThree[0].status === 'done' && formState.filesThree[0].originFileObj) {
            console.log('=== 开始上传试卷文件 ===', submitCounter, formState.filesThree[0]);
            const addResResult: any = await uploadFile(formState.filesThree[0].originFileObj, {
              moduleCode: '140-1',
              grade: formState.grade,
              press: formState.press,
              subject: formState.subject,
              volume: formState.volume,
              seconds: 0,
            });
            console.log('=== 试卷文件上传完成 ===', submitCounter, addResResult);
            if (addResResult && addResResult.success) {
              values.fileIdThree = addResResult.result.id;
              values.fileNameThree = addResResult.result.fileName;
              values.fileSizeThree = addResResult.result.fileSize;
              values.fileUrlThree = addResResult.result.filePath;
            }
          }
        } else {
          values.fileIdThree = undefined;
          values.fileNameThree = undefined;
          values.fileSizeThree = undefined;
          values.fileUrlThree = undefined;
        }

        if (formState.types.includes('101')) {
          formatValue.configResList.push({
            name: formState.nameOne,
            fileId: values.fileIdOne,
            fileName: values.fileNameOne,
            fileSize: values.fileSizeOne,
            fileUrl: values.fileUrlOne,
            text: formState.textOne,
            type: '101',
          });
        }
        if (formState.types.includes('102')) {
          formatValue.configResList.push({
            name: formState.nameTwo,
            fileId: values.fileIdTwo,
            fileName: values.fileNameTwo,
            fileSize: values.fileSizeTwo,
            fileUrl: values.fileUrlTwo,
            text: formState.textTwo,
            type: '102',
          });
        }
        if (formState.types.includes('103')) {
          formatValue.configResList.push({
            name: formState.nameThree,
            fileId: values.fileIdThree,
            fileName: values.fileNameThree,
            fileSize: values.fileSizeThree,
            fileUrl: values.fileUrlThree,
            text: formState.textThree,
            type: '103',
          });
        }
        //提交表单
        await addOrUpdate(formatValue, isUpdate.value);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success');
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error) => {
      // 在表单重置后，滚动到顶部
      console.log(`output->error`, error);
      nextTick(() => {
        // 获取模态框内可滚动的内容区域
        var modalContent = document.querySelector('.ant-modal-body .scrollbar__wrap.scrollbar__wrap--hidden-default') as HTMLElement;
        console.log(`output->modalContent`, modalContent);
        // 使用scrollTo方法滚动到顶部
        modalContent.scrollTo({
          top: 0,
          behavior: 'smooth', // 平滑滚动
        });
      });
    });
}
</script>
<style lang="less" scoped>
.form {
  padding: 10px;
}
.ant-form-item {
  margin-bottom: 16px;
}
.config-box {
  padding: 16px;
  border-radius: 2px;
  background: #ffffff;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
  margin-bottom: 16px;
}
.bc-upload-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  border-radius: 2px;
  background: #fafafa;
  padding: 0 16px;
  margin-top: 10px;
  .upload-tips-left {
    display: flex;
    font-family: '苹方-简';
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0px;

    color: rgba(0, 0, 0, 0.85);
    .filename {
      // flex: 0 0 auto;
      width: 160px;
      min-width: 160px;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; // 默认不换行；
    }
    .filesize {
      flex: 0 0 90px;
      padding-left: 8px;
    }
    .filestatus {
      flex: 0 0 177px;
    }
  }
}
</style>
