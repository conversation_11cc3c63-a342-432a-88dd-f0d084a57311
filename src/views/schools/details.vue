<template>
  <div class="school-details-container app-container">
    <el-row :gutter="20">
      <!-- 左侧学校信息 -->
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <template #header>
            <div class="clearfix">
              <span>学校信息</span>
            </div>
          </template>
          <div class="school-info">
            <div class="school-info-item">
              <!-- 学校Logo -->
              <el-avatar
                :size="100"
                :src="schoolData.schoolLogo || defaultLogo"
                style="margin-bottom: 10px"
              >
                <el-image
                  :src="schoolData.schoolLogo || defaultLogo"
                  fit="cover"
                  style="width: 100%; height: 100%"
                >
                </el-image>
              </el-avatar>
            </div>
            <!-- 学校名称 -->
            <div class="school-info-item">
              <h3 class="school-main-name">{{ schoolData.schoolName }}</h3>
            </div>
            <!-- 操作按钮 -->
            <div class="school-info-item">
              <el-button
                type="primary"
                style="margin-right: 10px"
                @click="openFinishedHomeworkDialog"
              >
                成品作业管理
              </el-button>

              <el-button
                type="primary"
                style="margin-right: 10px"
                @click="openAdminSettingDialog"
              >
                管理员设置
              </el-button>
            </div>
            <!-- 详细信息描述列表 -->
            <el-descriptions :column="1" border class="margin-top">
              <el-descriptions-item
                label-class-name="info-label"
                label="系统名称"
                >{{ schoolData.schoolXbName || "--" }}</el-descriptions-item
              >

              <el-descriptions-item label-class-name="info-label" label="域名">
                <el-link type="primary">{{ schoolData.schoolDomain }}</el-link>
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="info-label"
                label="学校编号"
                >{{ schoolData.schoolNumber || "--" }}</el-descriptions-item
              >
              <el-descriptions-item label-class-name="info-label" label="学段">
                <dict-tag
                  :options="sys_stage"
                  :value="schoolData.schoolStage || '--'"
                />
              </el-descriptions-item>
              <el-descriptions-item label-class-name="info-label" label="学制">
                <dict-tag
                  :options="sys_system"
                  :value="schoolData.schoolSystem || '--'"
                />
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="info-label"
                label="办学性质"
              >
                <dict-tag
                  :options="sys_school_nature"
                  :value="schoolData.schoolNature || '--'"
                />
              </el-descriptions-item>
              <el-descriptions-item
                label-class-name="info-label"
                label="地区"
                >{{ getRegionName(schoolData) }}</el-descriptions-item
              >
              <el-descriptions-item
                label-class-name="info-label"
                label="详细地址"
                >{{ schoolData.schoolAddress || "--" }}</el-descriptions-item
              >
              <el-descriptions-item
                label-class-name="info-label"
                label="联系方式"
                >{{ schoolData.schoolPhone || "--" }}</el-descriptions-item
              >
              <el-descriptions-item
                label-class-name="info-label"
                label="学校简介"
                content-class-name="profile-content"
                >{{ schoolData.schoolMessage || "--" }}</el-descriptions-item
              >
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
      <!-- 右侧标签页 -->
      <el-col :span="18" :xs="24">
        <el-card>
          <!-- 使用配置驱动的动态 Tabs -->
          <el-tabs v-model="activeTab">
            <el-tab-pane
              lazy
              v-for="tab in visibleTabs"
              :key="tab.name"
              :label="tab.label"
              :name="tab.name"
            >
              <!-- keep-alive 保持子组件状态 -->
              <keep-alive>
                <component
                  v-if="activeTab === tab.name"
                  :is="tab.component"
                  :school-id="schoolData.schoolId"
                  :role-id="roleId"
                />
              </keep-alive>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 成品作业管理弹窗 -->
    <finished-homework-dialog
      :visible="finishedHomeworkDialogVisible"
      :dialog-data="{
        schoolName: schoolData.schoolName,
        schoolId: schoolData.schoolId,
      }"
      @close="handleFinishedHomeworkDialogClose"
    />

    <!-- 管理员设置弹窗 -->
    <admin-settings-dialog
      :visible="adminSettingDialogVisible"
      :dialog-data="{
        schoolName: schoolData.schoolName,
        schoolId: schoolData.schoolId,
        deptId: schoolData.deptId,
      }"
      @close="handleAdminSettingDialogClose"
    />
  </div>
</template>

<script setup name="SchoolDetails">
import {
  ref,
  onMounted,
  getCurrentInstance,
  computed,
  defineAsyncComponent,
} from "vue";
import { useRoute } from "vue-router";
import { getSchoolInfo } from "@/api/school"; // 假设获取学校详情的API
import defaultLogoPlaceholder from "@/assets/logo/logo.png"; // 引入默认logo图片 (请确保路径正确或替换为实际logo)
import { getRegionName } from "@/utils"; // 引入区域工具函数
// 引入相关组件
import FinishedHomeworkDialog from "./components/dialogs/FinishedHomeworkDialog.vue"; // 引入成品作业弹窗
import AdminSettingsDialog from "./components/dialogs/AdminSettingsDialog.vue"; // 引入管理员设置弹窗

const route = useRoute();
const { proxy } = getCurrentInstance(); // 获取全局方法，如 $modal
const { sys_stage, sys_school_nature, sys_system } = proxy.useDict(
  "sys_stage",
  "sys_school_nature",
  "sys_system"
);

// 学校数据模型
const schoolData = ref({});

// 角色ID
const roleId = ref(route.query.roleId);

// 默认LOGO图片引用
const defaultLogo = ref(defaultLogoPlaceholder);

// 动态组件采用懒加载，按需加载 JS 资源
const tabConfig = [
  {
    label: "班级信息",
    name: "classInfo",
    component: defineAsyncComponent(() => import("./components/classInfo.vue")),
  },
  {
    label: "未分班人员",
    name: "unassignedStaff",
    component: defineAsyncComponent(() =>
      import("./components/unassignedStaff.vue")
    ),
  },
  {
    label: "操作日志",
    name: "operationLog",
    component: defineAsyncComponent(() =>
      import("./components/operationLog.vue")
    ),
  },
];

// 根据权限等条件过滤可见 tab，这里默认全部可见
const visibleTabs = computed(() => tabConfig);

// --- 弹窗状态管理 ---
const finishedHomeworkDialogVisible = ref(false);
const adminSettingDialogVisible = ref(false);

// --- 打开成品作业弹窗 ---
const openFinishedHomeworkDialog = () => {
  finishedHomeworkDialogVisible.value = true;
};

// --- 关闭成品作业弹窗 ---
const handleFinishedHomeworkDialogClose = () => {
  finishedHomeworkDialogVisible.value = false;
};

// --- 打开管理员设置弹窗 ---
const openAdminSettingDialog = () => {
  adminSettingDialogVisible.value = true;
};

// --- 关闭管理员设置弹窗 ---
const handleAdminSettingDialogClose = () => {
  adminSettingDialogVisible.value = false;
};

const teacherTableData = ref([]); // 老师表格数据
const selectedTeachers = ref([]); // 存储选中的老师
const teacherPagination = ref({
  currentPage: 1,
  pageSize: 10, // 对应截图中的 "10 条/页"
  total: 0, // 初始总数，将从API获取
});

// 模拟老师数据加载
const mockTeacherData = (page, size) => {
  const totalRecords = 317982; // 截图中的总数
  const start = (page - 1) * size;
  const end = Math.min(start + size, totalRecords);
  const items = [];
  for (let i = start + 1; i <= end; i++) {
    // 为了模拟截图中的姓名，这里简单处理
    const names = ["张一", "张二", "张三", "张四", "张五"];
    items.push({
      id: i, // 序号
      name: names[(i - 1) % names.length], // 循环使用名字
      phone: `1332222${String(3333 + i).slice(-4)}`, // 模拟手机号
      gender: i % 2 === 0 ? "女" : "男", // 交替性别
      lastOperator: "管理员",
      lastOperationTime: `2025-01-${String(10 + (i % 5)).padStart(
        2,
        "0"
      )} 12:12:12`, // 模拟时间
      selected: false, // 用于复选框状态（el-table内部处理）
    });
  }
  return { items, total: totalRecords };
};

// 获取老师列表数据
const getTeacherList = () => {
  // loading.value = true;
  const data = mockTeacherData(
    teacherPagination.value.currentPage,
    teacherPagination.value.pageSize
  );
  teacherTableData.value = data.items;
  teacherPagination.value.total = data.total;
  // loading.value = false;
};

// 组件挂载后执行
onMounted(() => {
  getSchoolDetails(); // 获取学校详情
  getTeacherList(); // 获取未分班老师列表
});

// 获取学校详情数据
async function getSchoolDetails() {
  const schoolIdFromRoute = route.query.schoolId; // 从路由参数中获取 schoolId
  if (!schoolIdFromRoute) {
    // 如果 schoolId 不存在
    proxy.$modal.msgError("学校ID未提供"); // 显示错误提示
    // 返回学校列表
    return proxy.$router.push({ path: "schools" });
  }
  // loading.value = true; // 开始加载状态
  try {
    const { code, data } = await getSchoolInfo(schoolIdFromRoute); // 异步获取学校信息
    // 同时检查 response.data 是否存在，确保后续操作的安全性
    if (code === 200 && data) {
      schoolData.value = data; // 更新学校数据
      console.log("成功获取学校详情，ID:", data); // 打印成功日志
    } else {
      const errorMessage = data
        ? data.toString()
        : "获取学校详情失败，响应格式不正确或数据为空";
      throw new Error(errorMessage);
    }
  } catch (error) {
    // 捕获异步操作中的错误或主动抛出的错误
    proxy.$modal.msgError(error.message || "获取学校详情失败"); // 显示错误提示
    console.error("获取学校详情失败:", error); // 在控制台打印错误信息
  } finally {
    // loading.value = false; // 结束加载状态，无论成功或失败
  }
}
// 老师表格：处理选中项变化
const handleTeacherSelectionChange = (selection) => {
  selectedTeachers.value = selection;
};
// 老师表格：重置密码操作
const resetPassword = (row) => {
  proxy.$modal
    .confirm(`确定要重置【${row.name}】的密码吗？`)
    .then(() => {
      console.log("重置密码:", row);
      proxy.$modal.msgSuccess("重置密码操作成功（模拟）");
    })
    .catch(() => {});
};

// 通用编辑人员操作
const editStaff = (row, type) => {
  proxy.$modal.msgSuccess(
    `编辑【${type === "teacher" ? "老师" : "学生"} ${row.name}】操作待实现`
  );
  // 弹出编辑对话框等
};

// 通用删除人员操作
const deleteStaff = (row, type) => {
  proxy.$modal
    .confirm(
      `确定要删除【${type === "teacher" ? "老师" : "学生"} ${row.name}】吗？`
    )
    .then(() => {
      // 实际项目中应调用API删除
      if (type === "teacher") {
        teacherTableData.value = teacherTableData.value.filter(
          (item) => item.id !== row.id
        );
        teacherPagination.value.total--; // 简单处理总数，实际应刷新列表
        // 如果当前页没有数据了，可能需要跳到前一页
      }
      proxy.$modal.msgSuccess("删除成功（模拟）");
    })
    .catch(() => {});
};

// 老师表格：每页条数变化
const handleTeacherSizeChange = (newSize) => {
  teacherPagination.value.pageSize = newSize;
  teacherPagination.value.currentPage = 1; // 条数变化时回到第一页
  getTeacherList();
};

// 老师表格：当前页码变化
const handleTeacherCurrentChange = (newPage) => {
  teacherPagination.value.currentPage = newPage;
  getTeacherList();
};

// 当前激活的主标签页 —— 默认取配置第一项
const activeTab = ref(tabConfig[0].name);
</script>


<style lang="scss" scoped>
.school-details-container {
  .box-card {
    margin-bottom: 20px;
    // 卡片头部样式
    ::v-deep .el-card__header {
      background-color: #f8f9fa; // 淡灰色背景
      padding: 12px 20px; // 调整内边距
      .clearfix span {
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
  .school-info {
    text-align: center; // 左侧信息内容居中
    .school-info-item {
      margin-bottom: 15px;
      .school-main-name {
        font-size: 18px; // 调整学校主名称字体大小
        font-weight: bold;
        margin-top: 0;
        margin-bottom: 15px; // 与下方按钮的间距
        color: #333;
      }
      .el-button {
        // min-width: 120px; // 确保按钮有一定最小宽度
      }
    }
    .margin-top {
      margin-top: 20px;
    }
    // 自定义描述列表标签样式
    ::v-deep .info-label {
      font-weight: 500; // 标签字体不过于粗犷
      width: 120px; // 统一标签宽度
      text-align: right !important; // 确保标签右对齐
      background-color: #fafafa !important; // 标签背景色
      color: #555; // 标签文字颜色
      padding-right: 10px !important; // 标签右侧内边距
    }
    // 自定义描述列表内容样式
    ::v-deep .el-descriptions__content {
      text-align: left; // 内容左对齐
      color: #333; // 内容文字颜色
      word-break: break-all; // 长单词或URL换行
    }
    // 学校简介内容特殊处理，允许更多空间和左对齐
    ::v-deep .profile-content {
      text-align: left !important;
      display: block; // 确保简介内容块状显示，可以换行
      line-height: 1.6;
    }
  }

  // 右侧Tabs样式
  .el-tabs {
    // 主Tabs
    ::v-deep .el-tabs__header {
      margin-bottom: 20px;
    }
    ::v-deep .el-tabs__nav-wrap::after {
      // 移除主tabs下划线
      display: none;
    }
    ::v-deep .el-tabs__item {
      font-size: 15px;
      &.is-active {
        font-weight: bold;
      }
    }
    // 子Tabs (未分班人员下的老师/学生)
    .el-tabs--card {
      ::v-deep .el-tabs__header {
        border-bottom: 1px solid #e4e7ed; // 卡片式tabs头部边框
        margin-bottom: 15px;
      }
      ::v-deep .el-tabs__item {
        font-size: 14px;
      }
    }
  }

  // 表格操作按钮样式
  .el-table {
    .el-button--small {
      // 使操作列的按钮更紧凑
      padding: 4px 8px;
      margin: 0 3px;
    }
  }
}
</style>