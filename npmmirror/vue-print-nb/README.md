# vue-print-nb-bcbook

This is a directive wrapper for printed, Simple, fast, convenient, light.
( 特定改造版本： 解决IE兼容问题和支持Canvas自适应打印 )

## Install

#### NPM
```bash
npm install vue-print-nb-bcbook --save
```

```javascript
import Print from 'vue-print-nb-bcbook'

Vue.use(Print);
```


## Description

#### Print the entire page:

```
<button v-print>Print the entire page</button>
```


#### Print local range:

HTML:
```
    <div id="printMe" style="background:red;">
        <p>葫芦娃，葫芦娃</p>
        <p>一根藤上七朵花 </p>
        <p>小小树藤是我家 啦啦啦啦 </p>
        <p>叮当当咚咚当当　浇不大</p>
        <p> 叮当当咚咚当当 是我家</p>
        <p> 啦啦啦啦</p>
        <p>...</p>
    </div>

    <button v-print="'#printMe'">Print local range</button>
```


## License

[MIT](http://opensource.org/licenses/MIT)


## 上传NPM官仓
- 1、点击 prepublish 生成lib文件
- 2、npm publish
- 3、npm unpublish vue-print-nb-bcbook --force

