{"_from": "vue-print-nb-bcbook@^1.0.12", "_id": "vue-print-nb-bcbook@1.0.12", "_inBundle": false, "_integrity": "sha512-jHyWm6/TxB1iU2nHL7upQdHVdxb1SJQ9n3XKeYTaruFdbSphLo1vDtTunS2qVCjupk8lui7FlF5rxxSNr0zjZg==", "_location": "/vue-print-nb-bcbook", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vue-print-nb-bcbook@^1.0.12", "name": "vue-print-nb-bcbook", "escapedName": "vue-print-nb-bcbook", "rawSpec": "^1.0.12", "saveSpec": null, "fetchSpec": "^1.0.12"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/vue-print-nb-bcbook/-/vue-print-nb-bcbook-1.0.12.tgz", "_shasum": "975e1dac3da8c9736ac81b82a2b099cafe1bb3df", "_spec": "vue-print-nb-bcbook@^1.0.12", "_where": "/Users/<USER>/Documents/BC/bcbook-web", "author": "", "bundleDependencies": false, "dependencies": {"babel-plugin-transform-runtime": "^6.23.0"}, "deprecated": false, "description": "This is a directive wrapper for printed, Simple, fast, convenient, light. ( 特定改造版本： 解决IE兼容问题和支持Canvas自适应打印 )", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.24.1"}, "license": "ISC", "main": "index.es5.js", "name": "vue-print-nb-bcbook", "scripts": {"compile": "babel -d lib/ src/", "index": "babel index.js --out-file index.es5.js", "prepublish": "npm run compile & npm run index", "publish": "npm publish", "test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.12"}